import { createApp } from "vue";
import { createPinia } from "pinia";
import "virtual:uno.css";
import "./styles/index.css";
import App from "./App.vue";
import router from "./router";
import { initializeDatabase } from "./services/database/index";
import { useRSSStore } from "./stores/rssStore";
import { useSettingsStore } from "./stores/settingsStore";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate"; // 导入插件
import { MessageAPI } from "./components/Message";

// 开发环境下导入测试工具
if (import.meta.env?.DEV) {
  import("./utils/test-shiki");
}

const app = createApp(App);
const pinia = createPinia();

// 使用持久化插件
pinia.use(piniaPluginPersistedstate);

// 初始化数据库
initializeDatabase()
  .then(() => {
    // 使用 Pinia 状态管理
    app.use(pinia);

    // 使用路由
    app.use(router);

    // 挂载应用
    app.mount("#app");

    // 在全局定义同步函数，供 preload 脚本调用
    (window as any).performSync = async () => {
      console.log("执行同步操作");

      try {
        // 获取 RSS Store 实例
        const rssStore = useRSSStore();
        const settingsStore = useSettingsStore();

        // 触发全局事件显示同步弹窗
        window.dispatchEvent(new CustomEvent("show-sync-modal"));

        if (settingsStore.autoSync) {
          // 执行同步操作
          await rssStore.syncAllRSSFeeds();
        }

        if (settingsStore.autoCacheRetention) {
          // 执行缓存清理操作
          await settingsStore.autoCleanCache();
        }

        // 触发全局事件隐藏同步弹窗
        window.dispatchEvent(new CustomEvent("hide-sync-modal"));

        console.log("同步操作完成");
      } catch (error) {
        console.error("同步操作失败:", error);

        // 触发全局事件隐藏同步弹窗
        window.dispatchEvent(new CustomEvent("hide-sync-modal"));

        // 显示错误通知
        MessageAPI.error("RSS同步失败");
      }
    };

    console.log("performSync 函数已定义");
  })
  .catch((error) => {
    console.error("数据库初始化失败:", error);
  });
