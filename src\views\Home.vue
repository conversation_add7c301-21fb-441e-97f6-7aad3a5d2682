<template>
  <Layout>
    <div class="home">
      <!-- 加载状态 -->
      <div v-if="rssStore.loading" class="loading obsidian-card">
        <div class="loading-spinner"></div>
        <p class="obsidian-text-muted">正在加载文章...</p>
      </div>

      <!-- 文章列表 -->
      <div
        v-if="!rssStore.loading && rssStore.articles.length > 0"
        class="articles-container"
      >
        <div
          v-for="(article, index) in rssStore.articles"
          :key="`${article.id}-${index}`"
          class="rss-item obsidian-card"
        >
          <div class="article-content">
            <h3 class="item-title">
              <a
                href="#"
                @click.prevent="viewArticle(article)"
                class="item-link obsidian-text"
              >
                {{ article.title }}
              </a>
            </h3>
            <p class="item-description obsidian-text-muted">
              {{ article.contentSnippet || article.content || "暂无摘要" }}
            </p>
            <div class="item-meta">
              <span class="item-date obsidian-text-muted">{{
                formatDate(article.pubDate)
              }}</span>
              <span class="source-name obsidian-text-muted">
                作者: {{ article.creator || "未知" }}
              </span>
              <div class="item-actions">
                <!-- <button @click="openLink(article.link)" class="action-btn">
                  <div class="i-material-symbols-link-rounded" />
                  原文
                </button> -->
                <button
                  @click="handleCopyLink(article.link)"
                  class="copy-link-btn"
                >
                  <div class="i-material-symbols-content-copy-outline" />
                  复制链接
                </button>
              </div>
            </div>
          </div>
          <div class="article-cover">
            <img
              v-if="getArticleImage(article)"
              :src="getArticleImage(article)"
              :alt="article.title"
              class="cover-image"
              @error="handleImageError"
            />
            <div
              v-else
              class="cover-placeholder"
              :style="{ backgroundColor: getAvatarColor(article.title) }"
            >
              {{ getTitleInitial(article.title) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!rssStore.loading && rssStore.articles.length === 0"
        class="empty-state obsidian-card"
      >
        <div class="empty-icon">
          <div class="i-material-symbols-article-outline" />
        </div>
        <h3 class="obsidian-text">暂无文章</h3>
        <p class="obsidian-text-muted">
          {{ emptyStateMessage }}
        </p>
      </div>
    </div>
  </Layout>
</template>

<script lang="ts" setup>
import { useRSSStore } from "@/stores/rssStore";
import type { RSSContentItem } from "@/types/rss/rss-content";
import Layout from "../components/Layout/Layout.vue";
import { computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  formatDate,
  getAvatarColor,
  getTitleInitial,
  getArticleImage,
  handleImageError,
} from "@/services/utils";
import { copyLink } from "@/utils/clipboard";

const router = useRouter();
const rssStore = useRSSStore();

// 计算空状态消息
const emptyStateMessage = computed(() => {
  if (!rssStore.currentSourceId || !rssStore.currentSourceType) {
    return "还没有订阅任何RSS源，点击左侧的'新建订阅'开始添加吧！";
  }

  if (rssStore.currentSourceType === "subscription") {
    return "该订阅源暂无文章，请稍后再试或检查订阅源设置。";
  } else if (rssStore.currentSourceType === "folder") {
    return "该文件夹下的订阅源暂无文章，请稍后再试或检查订阅源设置。";
  }

  return "暂无文章";
});

// 查看文章详情
const viewArticle = (article: RSSContentItem) => {
  // 将文章数据存储到全局变量中
  (window as any).currentArticle = article;

  // 跳转到文章详情页
  router.push("/rss/article");
};

// 打开链接
const openLink = (url: string) => {
  if ((window as any).utoolsAPI) {
    (window as any).utoolsAPI.openExternal(url);
  } else {
    window.open(url, "_blank");
  }
};

const handleCopyLink = async (url: string) => {
  try {
    await copyLink(url);
  } catch (error) {
    console.error("复制链接失败:", error);
  }
};

onMounted(async () => {
  await rssStore.fetchAllData();
});
</script>

<style scoped src="./Home.css"></style>
