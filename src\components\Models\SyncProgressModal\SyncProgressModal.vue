<!-- src/components/Models/SyncProgressModal/SyncProgressModal.vue -->
<template>
  <SimpleModal
    :show="show"
    :zIndex="2000"
    card-style="width: 300px"
    title="RSS 同步进度"
    @close="handleClose"
  >
    <div class="">
      <p class="text-sm text-zinc-300 dark:text-zinc-400 my-5 sync-source-text">
        {{ syncCurrentSource || "准备同步..." }}
      </p>
      <!-- 动态进度条 -->
      <div class="progress-container">
        <div
          class="progress-bar"
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>
      <div class="flex justify-between items-center mt-5">
        <span class="text-sm text-zinc-300 dark:text-zinc-400"
          >{{ progressPercentage }}% Complete</span
        >
        <div
          class="nav-header-btn"
          @click="() => (errorDetails = !errorDetails)"
        >
          Details
        </div>
      </div>
      <div v-show="errorDetails" class="bg-gray-500 h-30 mt-3 rounded-lg">
        {{ syncErrors.join("\n") }}
      </div>
    </div>
  </SimpleModal>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import SimpleModal from "../SimpleModal/SimpleModal.vue";

// 定义组件的属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  syncProgress: {
    type: Number,
    default: 0,
  },
  syncTotal: {
    type: Number,
    default: 0,
  },
  syncCurrentSource: {
    type: String as () => string | null,
    default: null,
  },
  syncErrors: {
    type: Array,
    default: () => [],
  },
  isSyncing: {
    type: Boolean,
    default: false,
  },
});

// 定义组件可以触发的事件
const emit = defineEmits(["close", "cancel"]);

const errorDetails = ref(false);

// 计算进度百分比
const progressPercentage = computed(() => {
  if (props.syncTotal === 0) return 0;
  return Math.round((props.syncProgress / props.syncTotal) * 100);
});

// 关闭模态框的方法
const handleClose = () => {
  if (!props.isSyncing) {
    emit("close");
  }
};
</script>

<style scoped src="./SyncProgressModal.css"></style>
