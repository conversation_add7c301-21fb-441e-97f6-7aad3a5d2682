<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> 代码高亮测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e5e7eb;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #374151;
            border-radius: 8px;
            background: #1f2937;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-button:disabled {
            background: #6b7280;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #111827;
            border-radius: 6px;
            border: 1px solid #374151;
        }
        
        .error {
            color: #ef4444;
        }
        
        .success {
            color: #10b981;
        }
        
        pre {
            background: #111827;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #374151;
        }
        
        code {
            font-family: 'Monaco', 'Consolas', 'Fira Code', monospace;
        }
    </style>
</head>
<body>
    <h1>Shiki 代码高亮测试</h1>
    
    <div class="test-section">
        <h2>基础功能测试</h2>
        <button class="test-button" onclick="testBasicHighlighting()">测试基础代码高亮</button>
        <div id="basic-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>HTML 处理测试</h2>
        <button class="test-button" onclick="testHtmlProcessing()">测试 HTML 代码块处理</button>
        <div id="html-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>测试用的 HTML 内容</h2>
        <p>这是一个包含代码的 HTML 内容示例：</p>
        <div id="test-content">
            <p>这里有一些 JavaScript 代码：</p>
            <pre><code>function hello() {
    console.log('Hello, World!');
    return 'success';
}</code></pre>
            
            <p>还有一些内联代码：<code>const x = 42;</code></p>
            
            <p>Python 代码示例：</p>
            <pre><code>def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))</code></pre>
        </div>
        
        <button class="test-button" onclick="processTestContent()">处理上面的内容</button>
        <div id="processed-content" class="result" style="display: none;"></div>
    </div>

    <script type="module">
        // 模拟 Shiki 服务
        class MockShikiService {
            async initialize() {
                console.log('初始化 Shiki 服务...');
                // 模拟初始化延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log('Shiki 服务初始化完成');
            }
            
            async highlightCode(code, language) {
                console.log('高亮代码:', { code: code.substring(0, 50) + '...', language });
                
                // 简单的语法高亮模拟
                let highlightedCode = code
                    .replace(/function/g, '<span style="color: #c678dd;">function</span>')
                    .replace(/const|let|var/g, '<span style="color: #c678dd;">$&</span>')
                    .replace(/console\.log/g, '<span style="color: #61afef;">console.log</span>')
                    .replace(/def|if|return|print/g, '<span style="color: #c678dd;">$&</span>')
                    .replace(/\b\d+\b/g, '<span style="color: #d19a66;">$&</span>')
                    .replace(/'[^']*'/g, '<span style="color: #98c379;">$&</span>')
                    .replace(/"[^"]*"/g, '<span style="color: #98c379;">$&</span>');
                
                return `<pre class="shiki" style="background: linear-gradient(135deg, #1f2937, #111827); padding: 20px; border-radius: 8px; border: 1px solid #374151;"><code style="color: #e5e7eb;">${highlightedCode}</code></pre>`;
            }
            
            async processCodeBlocks(html) {
                console.log('处理 HTML 中的代码块...');
                
                // 处理 <pre><code> 块
                const preCodeRegex = /<pre[^>]*><code[^>]*>([\s\S]*?)<\/code><\/pre>/gi;
                let processedHtml = html;
                
                const matches = Array.from(html.matchAll(preCodeRegex));
                for (const match of matches) {
                    const [fullMatch, codeContent] = match;
                    const decodedCode = this.decodeHtml(codeContent);
                    
                    if (decodedCode.trim()) {
                        const highlightedCode = await this.highlightCode(decodedCode);
                        processedHtml = processedHtml.replace(fullMatch, highlightedCode);
                    }
                }
                
                return processedHtml;
            }
            
            decodeHtml(html) {
                const div = document.createElement('div');
                div.innerHTML = html;
                return div.textContent || div.innerText || '';
            }
        }
        
        const mockShikiService = new MockShikiService();
        
        // 全局测试函数
        window.testBasicHighlighting = async function() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                await mockShikiService.initialize();
                
                const testCode = `function greet(name) {
    console.log('Hello, ' + name + '!');
    return true;
}

const result = greet('World');`;
                
                const highlighted = await mockShikiService.highlightCode(testCode, 'javascript');
                
                resultDiv.innerHTML = `
                    <p class="success">✅ 基础代码高亮测试成功！</p>
                    <h4>原始代码：</h4>
                    <pre><code>${testCode}</code></pre>
                    <h4>高亮后的代码：</h4>
                    ${highlighted}
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        };
        
        window.testHtmlProcessing = async function() {
            const resultDiv = document.getElementById('html-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const testHtml = `
                    <p>这是一个包含代码的段落：</p>
                    <pre><code>const x = 42;
console.log(x);</code></pre>
                    <p>还有更多内容...</p>
                `;
                
                const processed = await mockShikiService.processCodeBlocks(testHtml);
                
                resultDiv.innerHTML = `
                    <p class="success">✅ HTML 处理测试成功！</p>
                    <h4>原始 HTML：</h4>
                    <pre><code>${testHtml.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
                    <h4>处理后的结果：</h4>
                    ${processed}
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        };
        
        window.processTestContent = async function() {
            const resultDiv = document.getElementById('processed-content');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>处理中...</p>';
            
            try {
                const testContentDiv = document.getElementById('test-content');
                const originalHtml = testContentDiv.innerHTML;
                
                const processed = await mockShikiService.processCodeBlocks(originalHtml);
                
                resultDiv.innerHTML = `
                    <p class="success">✅ 内容处理完成！</p>
                    <h4>处理后的内容：</h4>
                    <div style="border: 1px solid #374151; padding: 15px; border-radius: 6px; background: #1f2937;">
                        ${processed}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 处理失败: ${error.message}</p>`;
            }
        };
    </script>
</body>
</html>
