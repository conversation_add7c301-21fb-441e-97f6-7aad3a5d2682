<template>
  <Layout>
    <div class="shiki-test-page">
      <h1><PERSON><PERSON> 代码高亮测试</h1>

      <div class="test-section">
        <h2>测试不同编程语言的代码高亮</h2>

        <button
          @click="testCodeHighlighting"
          class="test-btn"
          :disabled="testing"
        >
          {{ testing ? "测试中..." : "开始测试" }}
        </button>

        <div v-if="testResults.length > 0" class="test-results">
          <div
            v-for="(result, index) in testResults"
            :key="index"
            class="test-result"
          >
            <h3>{{ result.language }} 代码示例</h3>
            <div class="original-code">
              <h4>原始代码：</h4>
              <pre><code>{{ result.originalCode }}</code></pre>
            </div>
            <div class="highlighted-code">
              <h4>高亮后的代码：</h4>
              <div v-html="result.highlightedCode"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Layout from "../components/Layout";
import { shikiService } from "@/services/shiki";

const testing = ref(false);
const testResults = ref<
  Array<{
    language: string;
    originalCode: string;
    highlightedCode: string;
  }>
>([]);

// 测试代码示例
const testCodes = [
  {
    language: "JavaScript",
    code: `// JavaScript 示例
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

const result = fibonacci(10);
console.log('Fibonacci result:', result);

// 箭头函数和解构
const users = [
  { name: 'Alice', age: 25 },
  { name: 'Bob', age: 30 }
];

const names = users.map(({ name }) => name);`,
  },
  {
    language: "Python",
    code: `# Python 示例
def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

# 列表推导式
numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers if x % 2 == 0]

# 类定义
class Person:
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def greet(self):
        return f"Hello, I'm {self.name}"`,
  },
  {
    language: "TypeScript",
    code: `// TypeScript 示例
interface User {
  id: number;
  name: string;
  email?: string;
}

class UserService {
  private users: User[] = [];
  
  async fetchUser(id: number): Promise<User | null> {
    const response = await fetch(\`/api/users/\${id}\`);
    if (!response.ok) {
      throw new Error('Failed to fetch user');
    }
    return response.json();
  }
  
  addUser(user: Omit<User, 'id'>): User {
    const newUser: User = {
      id: Date.now(),
      ...user
    };
    this.users.push(newUser);
    return newUser;
  }
}`,
  },
  {
    language: "CSS",
    code: `/* CSS 示例 */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .card {
    padding: 1rem;
    margin: 1rem;
  }
}`,
  },
  {
    language: "JSON",
    code: `{
  "name": "rss-browser",
  "version": "1.0.0",
  "description": "RSS 浏览器应用",
  "main": "index.js",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.5.13",
    "shiki": "^3.14.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.2.1",
    "typescript": "^5.9.2",
    "vite": "^6.0.11"
  }
}`,
  },
];

const testCodeHighlighting = async () => {
  testing.value = true;
  testResults.value = [];

  try {
    console.log("🚀 开始测试 Shiki 代码高亮...");

    // 初始化 Shiki（如果还没有初始化）
    console.log("1. 初始化 Shiki 服务...");
    await shikiService.initialize();
    console.log("✅ Shiki 服务初始化成功");

    for (const testCode of testCodes) {
      try {
        console.log(`2. 测试 ${testCode.language} 代码高亮...`);
        const highlightedCode = await shikiService.highlightCode(testCode.code);
        console.log(`✅ ${testCode.language} 代码高亮成功`);

        testResults.value.push({
          language: testCode.language,
          originalCode: testCode.code,
          highlightedCode,
        });
      } catch (error) {
        console.error(`❌ 高亮 ${testCode.language} 代码失败:`, error);
        testResults.value.push({
          language: testCode.language,
          originalCode: testCode.code,
          highlightedCode: `<pre class="shiki-fallback"><code>${testCode.code
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")}</code></pre>`,
        });
      }
    }

    console.log("🎉 所有测试完成！");
  } catch (error) {
    console.error("❌ 代码高亮测试失败:", error);
    // 显示错误信息
    testResults.value = [
      {
        language: "Error",
        originalCode: error.message,
        highlightedCode: `<div class="error-message" style="color: #ef4444; padding: 20px; background: #1f2937; border: 1px solid #ef4444; border-radius: 8px;">
        <h3>测试失败</h3>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <p><strong>错误堆栈:</strong></p>
        <pre style="color: #fbbf24; font-size: 12px;">${
          error.stack || "无堆栈信息"
        }</pre>
      </div>`,
      },
    ];
  } finally {
    testing.value = false;
  }
};
</script>

<style scoped>
.shiki-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
}

.test-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 20px;
  transition: background-color 0.2s;
}

.test-btn:hover:not(:disabled) {
  background: #2563eb;
}

.test-btn:disabled {
  background: #6b7280;
  cursor: not-allowed;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-result {
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  background: #1f2937;
}

.test-result h3 {
  color: #f9fafb;
  margin-top: 0;
  margin-bottom: 20px;
}

.test-result h4 {
  color: #e5e7eb;
  margin-bottom: 10px;
  font-size: 14px;
}

.original-code {
  margin-bottom: 20px;
}

.original-code pre {
  background: #111827;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  font-family: "Monaco", "Consolas", "Fira Code", monospace;
  font-size: 14px;
  line-height: 1.5;
}

.original-code code {
  color: #e5e7eb;
}

.highlighted-code {
  /* 高亮代码的样式由 shiki-theme.css 提供 */
}
</style>
