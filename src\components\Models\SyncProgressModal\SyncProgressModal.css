.loader {
  --clr: #003cff;
  --load-time: 2s;
  outline: 5px solid var(--clr);
  outline-offset: 5px;
  position: relative;
  overflow: hidden;
  border-radius: 10rem;
  width: 10rem;
  padding: 0.5rem 8rem;
}

.loader::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 300%;
  height: 300%;
  background-color: var(--clr);
  z-index: 2;
  animation: loading var(--load-time) ease-in-out infinite;
}

@keyframes loading {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  height: 10px;
  background-color: #374151;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #656fac;
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
}

.sync-source-text {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  max-width: 100%; /* 确保不超过父容器宽度 */
  display: block; /* 确保元素是块级元素 */
}
