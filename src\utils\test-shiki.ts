import { shikiService } from '@/services/shiki';

// 测试 Shiki 服务的工具函数
export const testShikiService = async () => {
  console.log('🚀 开始测试 Shiki 服务...');
  
  try {
    // 1. 测试初始化
    console.log('1. 测试初始化...');
    await shikiService.initialize();
    console.log('✅ 初始化成功');
    
    // 2. 测试基础代码高亮
    console.log('2. 测试基础代码高亮...');
    const testCode = `function hello() {
  console.log('Hello, World!');
  return 'success';
}

const result = hello();`;
    
    const highlighted = await shikiService.highlightCode(testCode, 'javascript');
    console.log('✅ 基础代码高亮成功');
    console.log('高亮结果:', highlighted.substring(0, 100) + '...');
    
    // 3. 测试 HTML 处理
    console.log('3. 测试 HTML 处理...');
    const testHtml = `
      <p>这是一个包含代码的段落：</p>
      <pre><code>const x = 42;
console.log(x);</code></pre>
      <p>还有一些内联代码：<code>const y = 'hello';</code></p>
    `;
    
    const processedHtml = await shikiService.processCodeBlocks(testHtml);
    console.log('✅ HTML 处理成功');
    console.log('处理结果:', processedHtml.substring(0, 200) + '...');
    
    // 4. 测试不同语言
    console.log('4. 测试不同编程语言...');
    const languages = [
      { name: 'Python', code: 'def hello():\n    print("Hello, World!")\n    return True' },
      { name: 'TypeScript', code: 'interface User {\n  name: string;\n  age: number;\n}' },
      { name: 'CSS', code: '.container {\n  display: flex;\n  justify-content: center;\n}' }
    ];
    
    for (const lang of languages) {
      const result = await shikiService.highlightCode(lang.code);
      console.log(`✅ ${lang.name} 高亮成功`);
    }
    
    console.log('🎉 所有测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
};

// 测试文章内容处理
export const testArticleContentProcessing = async () => {
  console.log('🚀 开始测试文章内容处理...');
  
  const mockArticleContent = `
    <h1>技术文章标题</h1>
    <p>这是一篇包含代码示例的技术文章。</p>
    
    <h2>JavaScript 示例</h2>
    <p>下面是一个简单的 JavaScript 函数：</p>
    <pre><code>function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));</code></pre>
    
    <h2>Python 示例</h2>
    <p>这是对应的 Python 版本：</p>
    <pre><code>def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

print(fibonacci(10))</code></pre>
    
    <p>还有一些内联代码：<code>const result = fibonacci(5);</code></p>
    
    <h2>CSS 样式</h2>
    <pre><code>.fibonacci-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}</code></pre>
  `;
  
  try {
    const processedContent = await shikiService.processCodeBlocks(mockArticleContent);
    console.log('✅ 文章内容处理成功');
    console.log('处理后的内容长度:', processedContent.length);
    
    // 创建一个临时元素来显示结果
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processedContent;
    tempDiv.style.cssText = `
      position: fixed;
      top: 50px;
      right: 20px;
      width: 400px;
      max-height: 500px;
      overflow-y: auto;
      background: #1f2937;
      border: 1px solid #374151;
      border-radius: 8px;
      padding: 20px;
      z-index: 9999;
      color: #e5e7eb;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭';
    closeBtn.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      background: #ef4444;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    `;
    closeBtn.onclick = () => document.body.removeChild(tempDiv);
    
    tempDiv.appendChild(closeBtn);
    document.body.appendChild(tempDiv);
    
    console.log('📱 处理结果已显示在页面右侧');
    return true;
    
  } catch (error) {
    console.error('❌ 文章内容处理失败:', error);
    return false;
  }
};

// 将测试函数暴露到全局作用域（仅在开发环境）
if (import.meta.env.DEV) {
  (window as any).testShiki = testShikiService;
  (window as any).testArticleProcessing = testArticleContentProcessing;
  console.log('🔧 开发模式：测试函数已暴露到全局作用域');
  console.log('使用 testShiki() 测试基础功能');
  console.log('使用 testArticleProcessing() 测试文章处理');
}
