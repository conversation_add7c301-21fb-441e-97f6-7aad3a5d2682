<template>
  <Layout>
    <div class="rss-article-page">
      <!-- 文章内容 -->
      <div v-if="article" class="article-content">
        <!-- 文章标题 -->
        <h1 class="article-title" :class="settingsStore.fontSizeClass">
          {{ article.title }}
        </h1>

        <!-- 文章元信息 -->
        <div class="article-meta">
          <span class="article-date">
            <div class="i-material-symbols-calendar-today-outline" />
            {{ formatDate(article.pubDate) }}
          </span>
          <span v-if="article.creator" class="article-author">
            <div class="i-material-symbols-edit-outline" />
            {{ article.creator }}
          </span>
          <div class="article-actions">
            <button @click="openOriginalLink" class="action-btn">
              <div class="i-material-symbols-link-rounded" />
              查看原文
            </button>
            <button @click="copyArticleLink" class="action-btn">
              <div class="i-material-symbols-content-copy-outline" />
              复制链接
            </button>
            <button
              @click="toggleBookmark"
              class="action-btn"
              :class="{ bookmarked: isBookmarked }"
            >
              <div
                :class="
                  isBookmarked
                    ? 'i-material-symbols-star'
                    : 'i-material-symbols-star-outline'
                "
              />
              收藏
            </button>
          </div>
        </div>

        <!-- 分类标签 -->
        <div
          v-if="article.categories && article.categories.length > 0"
          class="article-categories"
        >
          <span
            v-for="category in article.categories"
            :key="category"
            class="category-tag"
          >
            {{ category }}
          </span>
        </div>

        <!-- 文章正文 -->
        <div class="article-body" :class="settingsStore.fontSizeClass">
          <!-- 如果有完整内容，显示完整内容 -->
          <div
            v-if="article.content"
            class="article-full-content obsidian-text"
            :class="settingsStore.fontSizeClass"
            v-html="sanitizeHTML(article.content)"
          ></div>

          <!-- 如果没有完整内容，显示摘要 -->
          <div
            v-else-if="article.contentSnippet"
            class="article-snippet obsidian-text"
            :class="settingsStore.fontSizeClass"
          >
            <p>{{ article.contentSnippet }}</p>
            <div class="snippet-notice">
              <div class="obsidian-text-muted snippet-text">
                <div class="i-material-symbols-description-outline" />
                这是文章摘要，完整内容请点击"查看原文"
              </div>
            </div>
          </div>

          <!-- 如果都没有，显示描述 -->
          <div
            v-else-if="article.description"
            class="article-description obsidian-text"
            :class="settingsStore.fontSizeClass"
          >
            <p>{{ article.description }}</p>
            <div class="snippet-notice">
              <div class="obsidian-text-muted snippet-text">
                <div class="i-material-symbols-description-outline" />
                这是文章描述，完整内容请点击"查看原文"
              </div>
            </div>
          </div>

          <!-- 都没有的情况 -->
          <div v-else class="no-content">
            <div class="obsidian-text-muted no-content-text">
              <div class="i-material-symbols-sentiment-dissatisfied-outline" />
              暂无文章内容，请点击"查看原文"阅读完整文章
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading obsidian-card">
        <div class="loading-spinner"></div>
        <p class="obsidian-text-muted">正在加载文章内容...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error obsidian-card">
        <h3 class="error-title">
          <div class="i-material-symbols-error-outline" />
          文章加载失败
        </h3>
        <p class="error-message">找不到文章内容</p>
      </div>
    </div>
    <InputTagModel
      :show="showTagModal"
      :article-id="article?.id || ''"
      :existing-tags="existingTags"
      @close="handleTagModalClose"
      @save="handleTagModalSave"
    />
  </Layout>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { ref, onMounted, watch, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import Layout from "../components/Layout";
import { useRSSStore } from "@/stores/rssStore";
import { SystemTag, TagType } from "@/types/rss/rss-content";
import InputTagModel from "../components/Models/InputTagModel/InputTagModel.vue";
import { formatDate } from "@/services/utils";
import { copyLink } from "@/utils/clipboard";
import { useSettingsStore } from "@/stores/settingsStore";
import { MessageAPI } from "@/components/Message";

// ==================== 路由和Store ====================
const route = useRoute();
const rssStore = useRSSStore();
const settingsStore = useSettingsStore();

// ==================== 基础响应式数据 ====================
const article = ref<any>(null);
const loading = ref(true);

// ==================== 标签管理相关数据 ====================
const showTagModal = ref(false);
const existingTags = ref<string[]>([]);

// ==================== 阅读体验相关数据 ====================
const isBookmarked = ref(false);

// ==================== 标签管理相关方法 ====================
// 打开标签选择器
const openTagSelector = async () => {
  if (!article.value?.id) {
    console.error("文章ID不存在");
    return;
  }

  try {
    // 获取文章现有的自定义标签
    existingTags.value = rssStore.getTags(article.value.id, TagType.CUSTOM);
    showTagModal.value = true;
  } catch (error) {
    console.error("获取现有标签失败:", error);
  }
};

// 处理标签模态框关闭
const handleTagModalClose = () => {
  showTagModal.value = false;
};

// 处理标签模态框保存
const handleTagModalSave = (tags: string[]) => {
  console.log("保存的标签:", tags);
  showTagModal.value = false;

  // 显示通知
  MessageAPI.success("标签已保存");
};

// 修改 toggleBookmark 方法
const toggleBookmark = async () => {
  if (!article.value?.id) {
    console.error("文章ID不存在");
    return;
  }

  try {
    // 使用toggleSystemTag切换书签标签
    const hasBookmark = await rssStore.toggleTag(
      article.value.id,
      TagType.SYSTEM,
      SystemTag.BOOKMARK
    );

    // 更新本地状态
    isBookmarked.value = hasBookmark;

    console.log(`文章 ${hasBookmark ? "已添加到" : "已从收藏中"}移除`);

    // 如果取消收藏，删除所有自定义标签
    if (!hasBookmark) {
      // 获取文章的所有自定义标签
      const customTags = rssStore.getTags(article.value.id, TagType.CUSTOM);

      // 删除每个自定义标签
      for (const tag of customTags) {
        await rssStore.toggleTag(article.value.id, TagType.CUSTOM, tag);
      }

      console.log(`已删除 ${customTags.length} 个自定义标签`);
    }

    // 如果添加了书签，打开标签选择器
    if (hasBookmark) {
      openTagSelector();
    } else {
      // 显示通知
      MessageAPI.info("已从收藏中移除，并清除了所有自定义标签");
    }
  } catch (error) {
    console.error("切换书签状态失败:", error);
    MessageAPI.error("操作失败，请重试");
  }
};

// ==================== 链接处理相关方法 ====================
// 修改 openLinkWithUBrowser 方法
const openLinkWithUBrowser = (url: string) => {
  if ((window as any).utoolsAPI) {
    try {
      if (settingsStore.useUBrowser && (window as any).utoolsAPI.ubrowser) {
        // 使用 ubrowser 打开链接
        (window as any).utoolsAPI.ubrowser
          .goto(url)
          .run({ width: 1200, height: 800 });
      } else if ((window as any).utoolsAPI.openExternal) {
        // 使用系统默认浏览器打开链接
        (window as any).utoolsAPI.openExternal(url);
      } else {
        // 降级处理：使用普通方式打开链接
        window.open(url, "_blank");
      }
    } catch (error) {
      console.error("打开链接失败:", error);
      // 降级处理：使用普通方式打开链接
      window.open(url, "_blank");
    }
  } else {
    // 如果不在utools环境中，使用普通方式打开链接
    window.open(url, "_blank");
  }
};

// 打开原文链接
const openOriginalLink = () => {
  if (article.value?.link) {
    openLinkWithUBrowser(article.value.link);
  }
};

// 复制链接
const copyArticleLink = async () => {
  if (article.value?.link) {
    try {
      await copyLink(article.value.link);
    } catch (error) {
      console.error("复制文章链接失败:", error);
      // 不需要额外处理，copyLink 函数内部已经处理了用户提示
    }
  }
};

// ==================== 数据加载相关方法 ====================
// 在 RSSArticle.vue 的 loadArticleData 函数中添加段落解析
const loadArticleData = () => {
  loading.value = true;
  // 从路由参数或全局数据中获取文章
  const articleData = route.params.article || (window as any).currentArticle;
  if (articleData) {
    if (typeof articleData === "string") {
      try {
        article.value = JSON.parse(articleData);
      } catch {
        article.value = null;
      }
    } else {
      article.value = articleData;
    }
  }

  // 检查文章是否已添加书签
  if (article.value?.id) {
    try {
      isBookmarked.value = rssStore.hasTag(
        article.value.id,
        TagType.SYSTEM,
        SystemTag.BOOKMARK
      );
    } catch (error) {
      console.error("检查书签状态失败:", error);
    }
  }

  // 解析文章段落
  if (article.value?.content) {
    rssStore.setCurrentArticleParagraphs(article.value.content);
  } else {
    rssStore.clearCurrentArticleParagraphs();
  }

  loading.value = false;
};

// 在 handleArticleChanged 函数中也添加段落解析
const handleArticleChanged = (event: CustomEvent) => {
  loading.value = true;
  article.value = event.detail;

  // 检查文章是否已添加书签
  if (article.value?.id) {
    try {
      isBookmarked.value = rssStore.hasTag(
        article.value.id,
        TagType.SYSTEM,
        SystemTag.BOOKMARK
      );
    } catch (error) {
      console.error("检查书签状态失败:", error);
    }
  }

  // 解析文章段落
  if (article.value?.content) {
    rssStore.setCurrentArticleParagraphs(article.value.content);
  } else {
    rssStore.clearCurrentArticleParagraphs();
  }

  loading.value = false;
};

// ==================== 监听器 ====================
// 监听全局变量 currentArticle 的变化
watch(
  () => (window as any).currentArticle,
  (newArticle) => {
    if (newArticle) {
      loadArticleData();
    }
  }
);

// ==================== 生命周期钩子 ====================
// 在组件挂载时添加事件监听
onMounted(() => {
  // 加载文章数据
  loadArticleData();

  // 添加自定义事件监听
  window.addEventListener(
    "article-changed",
    handleArticleChanged as EventListener
  );

  // 将链接处理函数暴露到全局作用域，以便在HTML中的onclick事件中使用
  (window as any).openLinkWithUBrowser = openLinkWithUBrowser;
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener(
    "article-changed",
    handleArticleChanged as EventListener
  );
});

// ==================== 工具函数 ====================

// HTML处理和安全清理
const sanitizeHTML = (html: string) => {
  // 首先进行基本的安全清理
  const cleanedHtml = html
    .replace(/<script[^>]*>.*?<\/script>/gi, "")
    .replace(
      /<iframe(?![^>]*src=["'](https:\/\/player\.bilibili\.com|https:\/\/www\.youtube\.com|https:\/\/player\.vimeo\.com))/gi,
      ""
    )
    .replace(/javascript:/gi, "")
    .replace(/on\w+="[^"]*"/gi, "");

  // 创建一个临时DOM元素来处理标题和图片
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = cleanedHtml;

  // 查找所有标题并添加ID
  const headers = tempDiv.querySelectorAll("h1, h2, h3, h4, h5, h6");
  headers.forEach((header, index) => {
    const text = header.textContent || `标题-${index}`;

    // 从 rssStore 中获取预先生成的ID
    const id =
      rssStore.paragraphIdMap.get(text) ||
      `para-${index}-${rssStore.hashCode(text)}`;

    // 设置ID属性
    header.setAttribute("id", id);
  });

  // 处理所有图片标签
  const images = tempDiv.querySelectorAll("img");
  images.forEach((img) => {
    // 添加错误处理
    img.setAttribute("onerror", "this.style.display='none';");

    // 获取原始src
    const src = img.getAttribute("src");
    if (!src) return;

    // 为云存储图片添加时间戳避免缓存问题
    const timestamp = new Date().getTime();

    // 腾讯云COS处理
    if (src.includes("file.myqcloud.com") || src.includes("qcloud.com")) {
      img.setAttribute(
        "src",
        `${src}${src.includes("?") ? "&" : "?"}_t=${timestamp}`
      );
      img.setAttribute("referrerpolicy", "no-referrer");
    }

    // 阿里云OSS处理
    if (src.includes("aliyuncs.com") || src.includes("oss-cn-")) {
      img.setAttribute(
        "src",
        `${src}${src.includes("?") ? "&" : "?"}_t=${timestamp}`
      );
      img.setAttribute("referrerpolicy", "no-referrer");
    }

    // 其他云存储服务处理（可根据需要添加更多）
    if (src.includes("amazonaws.com") || src.includes("s3.")) {
      // AWS S3
      img.setAttribute(
        "src",
        `${src}${src.includes("?") ? "&" : "?"}_t=${timestamp}`
      );
    }

    if (src.includes("blob.core.windows.net")) {
      // Azure Blob Storage
      img.setAttribute(
        "src",
        `${src}${src.includes("?") ? "&" : "?"}_t=${timestamp}`
      );
    }
  });

  // 返回处理后的HTML
  let result = tempDiv.innerHTML;

  // 为链接添加点击事件处理
  return result.replace(
    /<a([^>]*)href="([^"]*)"([^>]*)>/gi,
    (match, beforeAttrs, url, afterAttrs) => {
      // 检查链接是否有效
      if (!url || url.startsWith("#") || url.startsWith("javascript:")) {
        return match; // 保留原始链接，不添加点击事件
      }

      // 为有效链接添加点击事件
      return `<a${beforeAttrs}href="${url}"${afterAttrs} onclick="window.openLinkWithUBrowser('${url}'); return false;">`;
    }
  );
};
</script>

<style scoped src="./RSSArticle.css"></style>
