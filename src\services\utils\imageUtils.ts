/**
 * 图片处理相关工具函数
 * 用于图片提取、处理和错误处理
 */

/**
 * 从HTML内容中提取第一张图片
 * @param content HTML内容
 * @returns 图片URL或空字符串
 */
export function extractFirstImageFromContent(
  content: string | undefined
): string {
  if (!content) {
    return "";
  }
  // 使用正则表达式匹配img标签的src属性
  const imgRegex = /<img[^>]+src="([^"]+)"/i;
  const match = content.match(imgRegex);

  if (!match) {
    return "";
  }

  let imageUrl = match[1];

  // 为云存储图片添加时间戳避免缓存问题
  const timestamp = new Date().getTime();

  // 腾讯云COS处理
  if (
    imageUrl.includes("file.myqcloud.com") ||
    imageUrl.includes("qcloud.com")
  ) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  // 阿里云OSS处理
  if (imageUrl.includes("aliyuncs.com") || imageUrl.includes("oss-cn-")) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  // AWS S3处理
  if (imageUrl.includes("amazonaws.com") || imageUrl.includes("s3.")) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  // Azure Blob Storage处理
  if (imageUrl.includes("blob.core.windows.net")) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  return imageUrl;
}

/**
 * 处理图片加载错误
 * @param event 图片加载错误事件
 */
export const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;

  // 获取图片的原始src
  const originalSrc = img.getAttribute("data-original-src") || img.src;

  // 检查是否是云存储图片
  const isCloudStorageImage =
    originalSrc.includes("file.myqcloud.com") ||
    originalSrc.includes("qcloud.com") ||
    originalSrc.includes("aliyuncs.com") ||
    originalSrc.includes("oss-cn-") ||
    originalSrc.includes("amazonaws.com") ||
    originalSrc.includes("s3.") ||
    originalSrc.includes("blob.core.windows.net");

  if (isCloudStorageImage) {
    // 尝试使用不同的策略重新加载图片

    // 策略1: 添加no-referrer策略
    if (!img.hasAttribute("referrerpolicy")) {
      img.setAttribute("referrerpolicy", "no-referrer");
      // 重新加载图片
      img.src = originalSrc;
      return;
    }

    // 策略2: 添加新的时间戳
    const timestamp = new Date().getTime();
    const newSrc = `${originalSrc}${
      originalSrc.includes("?") ? "&" : "?"
    }_retry=${timestamp}`;
    img.src = newSrc;

    // 如果已经是重试后的URL，则隐藏图片
    if (img.src.includes("_retry=")) {
      img.style.display = "none";
    }
  } else {
    // 非云存储图片，直接隐藏
    img.style.display = "none";
  }
};

/**
 * 为图片元素添加云存储优化属性
 * @param img 图片元素
 */
export const optimizeCloudStorageImage = (img: HTMLImageElement) => {
  const src = img.getAttribute("src");
  if (!src) return;

  // 保存原始src
  img.setAttribute("data-original-src", src);

  // 为云存储图片添加优化属性
  if (
    src.includes("file.myqcloud.com") ||
    src.includes("qcloud.com") ||
    src.includes("aliyuncs.com") ||
    src.includes("oss-cn-")
  ) {
    // 添加no-referrer策略
    img.setAttribute("referrerpolicy", "no-referrer");

    // 添加时间戳避免缓存问题
    const timestamp = new Date().getTime();
    const optimizedSrc = `${src}${
      src.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
    img.setAttribute("src", optimizedSrc);
  }

  // 添加错误处理
  img.addEventListener("error", handleImageError);
};
